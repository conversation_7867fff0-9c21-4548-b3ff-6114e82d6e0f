import json
import re


def process_terms(
    arg1: list[dict[str, object]], arg2: str, arg3: list[dict[str, object]]
) -> dict[str, list[dict[str, object]]]:
    """
    对 arg1 中的每个英文简称：
      1. 在 arg2 文本中统计出现次数；
      2. 在 arg3 术语规范中查找同名项，并用 arg3 的 full_form 和 definition_cn 规范化输出；

    返回格式：
    {
      "result": [
        {
          "abbreviation": "...",
          "count": 123,
          "full_form": "...",
          "definition_cn": "..."
        },
        ...
      ]
    }
    """
    # 构建 arg3 的索引：abbreviation -> {full_form, definition_cn}
    norm_map: dict[str, dict[str, object]] = {
        item["abbreviation"]: {"full_form": item["full_form"], "definition_cn": item["definition_cn"]}
        for item in arg3
        if "abbreviation" in item
    }

    results: list[dict[str, object]] = []

    for entry in arg1:
        abbr = entry.get("英文简称") or entry.get("abbreviation")
        if not isinstance(abbr, str):
            continue

        # 全词匹配，忽略大小写
        pattern = rf"\b{re.escape(abbr)}\b"
        count = len(re.findall(pattern, arg2, flags=re.IGNORECASE))

        # 规范化：优先取 arg3 中的信息
        spec = norm_map.get(abbr)
        if spec:
            full_form = spec["full_form"]
            definition_cn = spec["definition_cn"]
        else:
            full_form = entry.get("英文全称", "")
            definition_cn = entry.get("中文术语", "")

        results.append({"abbreviation": abbr, "count": count, "full_form": full_form, "definition_cn": definition_cn})

    return {"result": results}


# 示例用法
if __name__ == "__main__":
    arg1 = [
        {"中文术语": "全分析集", "英文全称": "Full Analysis Set", "英文简称": "FAS", "source": "main"},
        {"中文术语": "无进展生存期", "英文全称": "Progression-Free Survival", "英文简称": "PFS", "source": "main"},
        {"中文术语": "暴露分析集", "英文全称": "Exposure Analysis Set", "英文简称": "EAS", "source": "main"},
        {"中文术语": "药代动力学浓度分析集", "英文全称": "PK concentration set", "英文简称": "PKCS", "source": "main"},
        {
            "中文术语": "MedDRA",
            "英文全称": "Medical Dictionary for Regulatory Activities",
            "英文简称": "MedDRA",
            "source": "main",
        },
    ]
    arg2 = "本次分析使用了 FAS 和 EAS 集合，对 PFS 进行评估；同时参考了 MedDRA 词典。"
    arg3 = [
        {"abbreviation": "FAS", "full_form": "Full Analysis Set", "definition_cn": "全分析集"},
        {"abbreviation": "PFS", "full_form": "Progression-Free Survival", "definition_cn": "无进展生存期"},
        {"abbreviation": "EAS", "full_form": "Exposure Analysis Set", "definition_cn": "暴露分析集"},
        {"abbreviation": "PKCS", "full_form": "PK Concentration Set", "definition_cn": "药代动力学浓度分析集"},
        {
            "abbreviation": "MedDRA",
            "full_form": "Medical Dictionary for Regulatory Activities",
            "definition_cn": "医学管制活动词典",
        },
    ]

    output = process_terms(arg1, arg2, arg3)
    print(json.dumps(output, ensure_ascii=False, indent=2))
