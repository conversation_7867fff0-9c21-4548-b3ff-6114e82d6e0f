import json


def convert_jsonl_to_result(filepath: str) -> dict:
    result_list = []

    with open(filepath, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if line:
                try:
                    obj = json.loads(line)
                    result_list.append(obj)
                except json.JSONDecodeError as e:
                    print(f"跳过非法 JSON 行: {line}\n错误: {e}")

    return {"result": result_list}


# 示例用法
if __name__ == "__main__":
    input_path = "./缩略语_去重.jsonl"
    output = convert_jsonl_to_result(input_path)

    # 打印或保存结果
    print(json.dumps(output, ensure_ascii=False, indent=2))
